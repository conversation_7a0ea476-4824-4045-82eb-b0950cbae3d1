<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>研选工场名片</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Arial', 'Microsoft YaHei', sans-serif;
        background: #1a1a1a;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
      }

      .business-card {
        width: 400px;
        height: 247px; /* 400 / 1.618 ≈ 247 */
        background: #2a2a35;
        border-radius: 16px;
        position: relative;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }

      .business-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
      }

      /* 科技感背景装饰 */
      .card-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0.1;
        background: linear-gradient(135deg, transparent 0%, #f94c30 50%, transparent 100%);
      }

      .card-bg::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -20%;
        width: 200px;
        height: 200px;
        border: 2px solid #f94c30;
        border-radius: 50%;
        opacity: 0.3;
      }

      .card-bg::after {
        content: '';
        position: absolute;
        bottom: -30%;
        left: -10%;
        width: 150px;
        height: 150px;
        border: 1px solid #f94c30;
        border-radius: 50%;
        opacity: 0.2;
      }

      /* 主要内容区域 */
      .card-content {
        position: relative;
        z-index: 2;
        padding: 24px;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }

      /* 头部区域 */
      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
      }

      .logo {
        height: 40px;
        width: auto;
      }

      .ai-badge {
        background: linear-gradient(45deg, #f94c30, #ff6b4a);
        color: white;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      /* 个人信息 */
      .personal-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .person-name {
        color: white;
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 8px;
        letter-spacing: 2px;
      }

      .person-title {
        color: #f94c30;
        font-size: 16px;
        margin-bottom: 20px;
        font-weight: 500;
      }

      .company-brief {
        color: #a0a0a0;
        font-size: 13px;
        margin-bottom: 20px;
      }

      /* 联系信息 */
      .contact-info {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .contact-item {
        display: flex;
        align-items: center;
        color: #e0e0e0;
        font-size: 11px;
        gap: 8px;
      }

      .contact-icon {
        width: 16px;
        height: 16px;
        background: #f94c30;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 8px;
        color: white;
        flex-shrink: 0;
      }

      /* 底部装饰线 */
      .card-footer {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 3px;
        background: linear-gradient(90deg, #f94c30, #ff6b4a, #f94c30);
      }

      /* 响应式设计 */
      @media (max-width: 480px) {
        .business-card {
          width: 320px;
          height: 198px; /* 320 / 1.618 ≈ 198 */
        }

        .card-content {
          padding: 20px;
        }

        .person-name {
          font-size: 24px;
        }

        .person-title {
          font-size: 14px;
        }

        .company-brief {
          font-size: 11px;
        }
      }
    </style>
  </head>
  <body>
    <div class="business-card">
      <div class="card-bg"></div>

      <div class="card-content">
        <div class="card-header">
          <img src="https://www.yanxuan.cloud/_nuxt/logo_rect_white.DQtusgkJ.png" alt="研选工场" class="logo" />
          <div class="ai-badge">AI Platform</div>
        </div>

        <div class="personal-info">
          <div class="person-name">张三</div>
          <div class="person-title">研发部经理</div>
          <div class="company-brief">研选工场（苏州）网络有限公司</div>
        </div>

        <div class="contact-info">
          <div class="contact-item">
            <div class="contact-icon">📱</div>
            <span>***********</span>
          </div>
          <div class="contact-item">
            <div class="contact-icon">📧</div>
            <span><EMAIL></span>
          </div>
          <div class="contact-item">
            <div class="contact-icon">🌐</div>
            <span>www.yanxuan.cloud</span>
          </div>
        </div>
      </div>

      <div class="card-footer"></div>
    </div>
  </body>
</html>
